package com.gw.membership.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import com.gw.membership.dto.InvitationCodeDTO;
import com.gw.membership.dto.InvitationRecordQuery;
import com.gw.membership.dto.MyInvitationRecordQuery;
import com.gw.membership.entity.InvitationCodeEntity;
import com.gw.membership.entity.InvitationHistoryEntity;

/**
 * 邀请码服务接口
 */
public interface InvitationService {

    /**
     * 创建用户邀请码
     */
    InvitationCodeEntity createInvitationCode(String username);

    /**
     * 获取用户的邀请码
     */
    InvitationCodeEntity getUserInvitationCode(String username);

    /**
     * 验证邀请码是否有效
     */
    boolean validateInvitationCode(String code);

    /**
     * 使用邀请码 - 返回邀请人ID
     */
    void useInvitationCode(String code, String newUsername);

    /**
     * 分页查询邀请码
     */
    IPage<InvitationCodeEntity> pageInvitationCodes(String username, Page<InvitationCodeDTO> page);

    PageInfo<InvitationHistoryEntity> getInvitationRecordPage(int pageNum, int pageSize, InvitationRecordQuery query);

}