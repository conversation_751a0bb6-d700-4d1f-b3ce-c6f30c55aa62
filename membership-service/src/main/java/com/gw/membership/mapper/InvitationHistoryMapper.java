package com.gw.membership.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.membership.dto.InvitationRecordQuery;
import com.gw.membership.dto.MyInvitationRecordQuery;
import com.gw.membership.entity.InvitationHistoryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface InvitationHistoryMapper extends BaseMapper<InvitationHistoryEntity> {
    @Select("SELECT * FROM t_invitation_history WHERE invitation_code = #{invitationCode} AND invitee = #{invitee} AND deleted = 0 LIMIT 1")
    InvitationHistoryEntity findFirstByInvitationCodeAndInvitee(@Param("invitationCode") String invitationCode, @Param("invitee") String invitee);

    List<InvitationHistoryEntity> page(InvitationRecordQuery query);
}
